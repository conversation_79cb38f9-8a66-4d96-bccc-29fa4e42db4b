import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON>,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for patient growth with segments
const patientGrowthData = [
  {
    month: "Jan",
    aesthetics: 120,
    corporate: 80,
    imaging: 150,
    oncology: 200,
    paediatrics: 100,
    womensHealth: 90,
    others: 60,
    target: 1500,
    growthRate: 5.2,
    remaining: 700, // For waterfall effect
  },
  {
    month: "Feb",
    aesthetics: 140,
    corporate: 90,
    imaging: 160,
    oncology: 220,
    paediatrics: 110,
    womensHealth: 100,
    others: 70,
    target: 1550,
    growthRate: 7.1,
    remaining: 660,
  },
  {
    month: "Mar",
    aesthetics: 150,
    corporate: 95,
    imaging: 170,
    oncology: 240,
    paediatrics: 120,
    womensHealth: 110,
    others: 75,
    target: 1600,
    growthRate: 8.3,
    remaining: 640,
  },
  {
    month: "Apr",
    aesthetics: 160,
    corporate: 100,
    imaging: 180,
    oncology: 260,
    paediatrics: 130,
    womensHealth: 120,
    others: 80,
    target: 1650,
    growthRate: 9.5,
    remaining: 620,
  },
  {
    month: "May",
    aesthetics: 170,
    corporate: 110,
    imaging: 190,
    oncology: 280,
    paediatrics: 140,
    womensHealth: 130,
    others: 85,
    target: 1700,
    growthRate: 10.2,
    remaining: 595,
  },
  {
    month: "Jun",
    aesthetics: 180,
    corporate: 120,
    imaging: 200,
    oncology: 300,
    paediatrics: 150,
    womensHealth: 140,
    others: 90,
    target: 1750,
    growthRate: 12.4,
    remaining: 570,
  },
  {
    month: "Jul",
    aesthetics: 190,
    corporate: 130,
    imaging: 210,
    oncology: 320,
    paediatrics: 160,
    womensHealth: 150,
    others: 95,
    target: 1800,
    growthRate: 13.8,
    remaining: 545,
  },
  {
    month: "Aug",
    aesthetics: 200,
    corporate: 140,
    imaging: 220,
    oncology: 340,
    paediatrics: 170,
    womensHealth: 160,
    others: 100,
    target: 1850,
    growthRate: 15.2,
    remaining: 520,
  },
]

// Segment colors
const segmentColors = {
  aesthetics: "#8B5CF6",
  corporate: "#06B6D4",
  imaging: "#10B981",
  oncology: "#F59E0B",
  paediatrics: "#EF4444",
  womensHealth: "#EC4899",
  others: "#6B7280",
  remaining: "transparent",
}

interface PatientGrowthChartProps {
  data?: typeof patientGrowthData
}

const PatientGrowthChart: React.FC<PatientGrowthChartProps> = ({
  data = patientGrowthData,
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : patientGrowthData

  // Calculate metrics
  const currentMonth = chartData[chartData.length - 1]
  const previousMonth = chartData[chartData.length - 2]

  const totalCurrentPatients =
    currentMonth.aesthetics +
    currentMonth.corporate +
    currentMonth.imaging +
    currentMonth.oncology +
    currentMonth.paediatrics +
    currentMonth.womensHealth +
    currentMonth.others

  const totalPreviousPatients =
    previousMonth.aesthetics +
    previousMonth.corporate +
    previousMonth.imaging +
    previousMonth.oncology +
    previousMonth.paediatrics +
    previousMonth.womensHealth +
    previousMonth.others

  const patientGrowth = totalCurrentPatients - totalPreviousPatients
  const patientGrowthPercent = Math.round(
    (patientGrowth / totalPreviousPatients) * 100
  )

  const avgMonthlyGrowth =
    Math.round(
      (chartData.reduce((sum: number, item: any) => sum + item.growthRate, 0) /
        chartData.length) *
        10
    ) / 10

  // Calculate if target was met
  const targetMet = totalCurrentPatients >= currentMonth.target
  const targetDiff = Math.abs(totalCurrentPatients - currentMonth.target)
  const targetDiffPercent = Math.round((targetDiff / currentMonth.target) * 100)

  // Calculate total patients for each month and prepare waterfall data
  const dataWithTotals = chartData.map((item: any) => {
    const totalSegments =
      item.aesthetics +
      item.corporate +
      item.imaging +
      item.oncology +
      item.paediatrics +
      item.womensHealth +
      item.others

    return {
      ...item,
      totalPatients: totalSegments,
    }
  })

  // Custom tooltip for waterfall chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="rounded border bg-white p-3 shadow-lg">
          <p className="font-semibold">{`${label}`}</p>
          <p className="text-purple-600">{`Aesthetics: ${formatAbbreviatedCurrency(data.aesthetics, 0)}`}</p>
          <p className="text-cyan-600">{`Corporate: ${formatAbbreviatedCurrency(data.corporate, 0)}`}</p>
          <p className="text-green-600">{`Imaging: ${formatAbbreviatedCurrency(data.imaging, 0)}`}</p>
          <p className="text-amber-600">{`Oncology: ${formatAbbreviatedCurrency(data.oncology, 0)}`}</p>
          <p className="text-red-600">{`Paediatrics: ${formatAbbreviatedCurrency(data.paediatrics, 0)}`}</p>
          <p className="text-pink-600">{`Women's Health: ${formatAbbreviatedCurrency(data.womensHealth, 0)}`}</p>
          <p className="text-gray-600">{`Others: ${formatAbbreviatedCurrency(data.others, 0)}`}</p>
          <hr className="my-1" />
          <p className="font-semibold">{`Total: ${formatAbbreviatedCurrency(data.totalPatients, 0)}`}</p>
          <p className="text-red-500">{`Target: ${formatAbbreviatedCurrency(data.target, 0)}`}</p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Total: {totalCurrentPatients.toLocaleString()}
          </div>
          <div
            className={`${patientGrowthPercent >= 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"} rounded-full px-2.5 py-0.5 text-xs font-medium`}
          >
            Growth: {patientGrowthPercent >= 0 ? "+" : ""}
            {patientGrowthPercent}%
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <BarChart
          data={dataWithTotals}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />

          {/* Transparent bar for waterfall effect */}
          <Bar dataKey="remaining" stackId="waterfall" fill="transparent" />

          {/* Stacked bars for each segment */}
          <Bar
            dataKey="aesthetics"
            name="Aesthetics"
            stackId="waterfall"
            fill={segmentColors.aesthetics}
          />
          <Bar
            dataKey="corporate"
            name="Corporate"
            stackId="waterfall"
            fill={segmentColors.corporate}
          />
          <Bar
            dataKey="imaging"
            name="Imaging"
            stackId="waterfall"
            fill={segmentColors.imaging}
          />
          <Bar
            dataKey="oncology"
            name="Oncology"
            stackId="waterfall"
            fill={segmentColors.oncology}
          />
          <Bar
            dataKey="paediatrics"
            name="Paediatrics"
            stackId="waterfall"
            fill={segmentColors.paediatrics}
          />
          <Bar
            dataKey="womensHealth"
            name="Women's Health"
            stackId="waterfall"
            fill={segmentColors.womensHealth}
          />
          <Bar
            dataKey="others"
            name="Others"
            stackId="waterfall"
            fill={segmentColors.others}
          />

          <ReferenceLine
            y={currentMonth.target}
            label="Target"
            stroke="red"
            strokeDasharray="3 3"
          />
        </BarChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2">
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Top Segment</p>
          <p className="text-lg font-bold text-purple-600">
            {currentMonth.oncology > currentMonth.aesthetics &&
            currentMonth.oncology > currentMonth.corporate &&
            currentMonth.oncology > currentMonth.imaging &&
            currentMonth.oncology > currentMonth.paediatrics &&
            currentMonth.oncology > currentMonth.womensHealth &&
            currentMonth.oncology > currentMonth.others
              ? "Oncology"
              : currentMonth.imaging > currentMonth.aesthetics &&
                  currentMonth.imaging > currentMonth.corporate &&
                  currentMonth.imaging > currentMonth.paediatrics &&
                  currentMonth.imaging > currentMonth.womensHealth &&
                  currentMonth.imaging > currentMonth.others
                ? "Imaging"
                : "Other"}
          </p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Monthly Growth</p>
          <p className="text-lg font-bold text-green-600">
            {avgMonthlyGrowth}%
          </p>
        </div>
        <div
          className={`${targetMet ? "bg-green-50" : "bg-yellow-50"} rounded p-2 text-center`}
        >
          <p className="text-xs font-medium text-gray-700">Target</p>
          <p
            className={`text-lg font-bold ${targetMet ? "text-green-600" : "text-yellow-600"}`}
          >
            {targetMet ? "Met" : `${targetDiffPercent}% Below`}
          </p>
        </div>
      </div>
    </div>
  )
}

export default PatientGrowthChart
