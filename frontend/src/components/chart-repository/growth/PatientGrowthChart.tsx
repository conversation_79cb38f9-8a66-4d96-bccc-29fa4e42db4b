import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON>ian<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for patient growth with segments (incremental monthly growth)
const patientGrowthData = [
  {
    month: "Jan",
    aesthetics: 5,
    corporate: 3,
    imaging: 4,
    oncology: 6,
    paediatrics: 3,
    womensHealth: 3,
    others: 2,
    growthRate: 5.2,
    remaining: 700, // For waterfall effect
  },
  {
    month: "Feb",
    aesthetics: 4,
    corporate: 2,
    imaging: 3,
    oncology: 5,
    paediatrics: 3,
    womensHealth: 2,
    others: 2,
    growthRate: 7.1,
    remaining: 660,
  },
  {
    month: "Mar",
    aesthetics: 6,
    corporate: 3,
    imaging: 5,
    oncology: 7,
    paediatrics: 4,
    womensHealth: 4,
    others: 3,
    growthRate: 8.3,
    remaining: 640,
  },
  {
    month: "Apr",
    aesthetics: 5,
    corporate: 3,
    imaging: 4,
    oncology: 6,
    paediatrics: 3,
    womensHealth: 3,
    others: 2,
    growthRate: 9.5,
    remaining: 620,
  },
  {
    month: "May",
    aesthetics: 6,
    corporate: 4,
    imaging: 5,
    oncology: 8,
    paediatrics: 4,
    womensHealth: 4,
    others: 3,
    growthRate: 10.2,
    remaining: 595,
  },
  {
    month: "Jun",
    aesthetics: 7,
    corporate: 4,
    imaging: 6,
    oncology: 9,
    paediatrics: 5,
    womensHealth: 5,
    others: 3,
    growthRate: 12.4,
    remaining: 570,
  },
]

// Segment colors
const segmentColors = {
  aesthetics: "#8B5CF6",
  corporate: "#06B6D4",
  imaging: "#10B981",
  oncology: "#F59E0B",
  paediatrics: "#EF4444",
  womensHealth: "#EC4899",
  others: "#6B7280",
  remaining: "transparent",
}

interface PatientGrowthChartProps {
  data?: typeof patientGrowthData
}

const PatientGrowthChart: React.FC<PatientGrowthChartProps> = ({
  data = patientGrowthData,
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : patientGrowthData

  // Calculate metrics
  const currentMonth = chartData[chartData.length - 1]
  const previousMonth = chartData[chartData.length - 2]

  const totalCurrentPatients =
    currentMonth.aesthetics +
    currentMonth.corporate +
    currentMonth.imaging +
    currentMonth.oncology +
    currentMonth.paediatrics +
    currentMonth.womensHealth +
    currentMonth.others

  const totalPreviousPatients =
    previousMonth.aesthetics +
    previousMonth.corporate +
    previousMonth.imaging +
    previousMonth.oncology +
    previousMonth.paediatrics +
    previousMonth.womensHealth +
    previousMonth.others

  const patientGrowth = totalCurrentPatients - totalPreviousPatients
  const patientGrowthPercent = Math.round(
    (patientGrowth / totalPreviousPatients) * 100
  )

  const avgMonthlyGrowth =
    Math.round(
      (chartData.reduce((sum: number, item: any) => sum + item.growthRate, 0) /
        chartData.length) *
        10
    ) / 10

  // Transform data for waterfall chart with stacked segments
  const transformedData = chartData.map((item: any, index: number) => {
    const totalSegments =
      item.aesthetics +
      item.corporate +
      item.imaging +
      item.oncology +
      item.paediatrics +
      item.womensHealth +
      item.others

    // Calculate cumulative base for waterfall effect
    // Start from 2024 baseline total, then add previous monthly growth
    const total2024Base = 100 + 60 + 120 + 150 + 80 + 70 + 50 // 630
    let cumulativeBase = total2024Base
    for (let i = 0; i < index; i++) {
      const prevItem = chartData[i]
      cumulativeBase +=
        prevItem.aesthetics +
        prevItem.corporate +
        prevItem.imaging +
        prevItem.oncology +
        prevItem.paediatrics +
        prevItem.womensHealth +
        prevItem.others
    }

    return {
      ...item,
      totalPatients: totalSegments,
      // Base for waterfall effect (2024 baseline + cumulative growth from previous months)
      base: cumulativeBase,
      // New total after this month's growth
      newTotal: cumulativeBase + totalSegments,
      // Each segment as individual data keys for stacking
      aesthetics: item.aesthetics,
      corporate: item.corporate,
      imaging: item.imaging,
      oncology: item.oncology,
      paediatrics: item.paediatrics,
      womensHealth: item.womensHealth,
      others: item.others,
      isTotal: false,
    }
  })

  // Calculate 2024 baseline data (starting values for each segment)
  const data2024 = {
    aesthetics: 100,
    corporate: 60,
    imaging: 120,
    oncology: 150,
    paediatrics: 80,
    womensHealth: 70,
    others: 50,
  }

  // Calculate 2025 totals (2024 baseline + cumulative growth from all months)
  const data2025 = {
    aesthetics:
      data2024.aesthetics +
      chartData.reduce((sum: number, item: any) => sum + item.aesthetics, 0),
    corporate:
      data2024.corporate +
      chartData.reduce((sum: number, item: any) => sum + item.corporate, 0),
    imaging:
      data2024.imaging +
      chartData.reduce((sum: number, item: any) => sum + item.imaging, 0),
    oncology:
      data2024.oncology +
      chartData.reduce((sum: number, item: any) => sum + item.oncology, 0),
    paediatrics:
      data2024.paediatrics +
      chartData.reduce((sum: number, item: any) => sum + item.paediatrics, 0),
    womensHealth:
      data2024.womensHealth +
      chartData.reduce((sum: number, item: any) => sum + item.womensHealth, 0),
    others:
      data2024.others +
      chartData.reduce((sum: number, item: any) => sum + item.others, 0),
  }

  const total2024 =
    data2024.aesthetics +
    data2024.corporate +
    data2024.imaging +
    data2024.oncology +
    data2024.paediatrics +
    data2024.womensHealth +
    data2024.others

  const total2025 =
    data2025.aesthetics +
    data2025.corporate +
    data2025.imaging +
    data2025.oncology +
    data2025.paediatrics +
    data2025.womensHealth +
    data2025.others

  // Create waterfall data with 2024 start and 2025 end
  const waterfallData = [
    // 2024 starting bar (stacked with all segments)
    {
      month: "2024",
      base: 0,
      newTotal: total2024,
      aesthetics: data2024.aesthetics,
      corporate: data2024.corporate,
      imaging: data2024.imaging,
      oncology: data2024.oncology,
      paediatrics: data2024.paediatrics,
      womensHealth: data2024.womensHealth,
      others: data2024.others,
      totalPatients: total2024,
      isTotal: true,
    },
    // Monthly data
    ...transformedData,
    // 2025 ending bar (stacked with all segments)
    {
      month: "2025",
      base: 0,
      newTotal: total2025,
      aesthetics: data2025.aesthetics,
      corporate: data2025.corporate,
      imaging: data2025.imaging,
      oncology: data2025.oncology,
      paediatrics: data2025.paediatrics,
      womensHealth: data2025.womensHealth,
      others: data2025.others,
      totalPatients: total2025,
      isTotal: true,
    },
  ]

  // Custom tooltip for waterfall chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      const isMonthlyGrowth = !data.isTotal

      return (
        <div className="rounded border border-gray-300 bg-white p-3 shadow-lg">
          <p className="mb-2 text-lg font-bold text-gray-900">{`${label}`}</p>
          <div className="space-y-1">
            {isMonthlyGrowth ? (
              <>
                <p className="font-medium text-gray-800">
                  Monthly Growth:{" "}
                  <span className="font-bold">
                    {formatAbbreviatedCurrency(data.totalPatients || 0, 0)}
                  </span>
                </p>
                <div className="space-y-1 text-sm">
                  <p className="text-purple-600">{`Aesthetics: +${formatAbbreviatedCurrency(data.aesthetics || 0, 0)}`}</p>
                  <p className="text-cyan-600">{`Corporate: +${formatAbbreviatedCurrency(data.corporate || 0, 0)}`}</p>
                  <p className="text-green-600">{`Imaging: +${formatAbbreviatedCurrency(data.imaging || 0, 0)}`}</p>
                  <p className="text-amber-600">{`Oncology: +${formatAbbreviatedCurrency(data.oncology || 0, 0)}`}</p>
                  <p className="text-red-600">{`Paediatrics: +${formatAbbreviatedCurrency(data.paediatrics || 0, 0)}`}</p>
                  <p className="text-pink-600">{`Women's Health: +${formatAbbreviatedCurrency(data.womensHealth || 0, 0)}`}</p>
                  <p className="text-gray-600">{`Others: +${formatAbbreviatedCurrency(data.others || 0, 0)}`}</p>
                </div>
                <hr className="my-2" />
                <p className="text-sm text-gray-700">
                  Previous Total:{" "}
                  <span className="font-semibold">
                    {formatAbbreviatedCurrency(data.base || 0, 0)}
                  </span>
                </p>
                <p className="text-sm text-gray-700">
                  New Total:{" "}
                  <span className="font-semibold">
                    {formatAbbreviatedCurrency(data.newTotal || 0, 0)}
                  </span>
                </p>
              </>
            ) : (
              <>
                <p className="font-medium text-gray-800">
                  Total Patients:{" "}
                  <span className="font-bold">
                    {formatAbbreviatedCurrency(data.totalPatients || 0, 0)}
                  </span>
                </p>
                <div className="space-y-1 text-sm">
                  <p className="text-purple-600">{`Aesthetics: ${formatAbbreviatedCurrency(data.aesthetics || 0, 0)}`}</p>
                  <p className="text-cyan-600">{`Corporate: ${formatAbbreviatedCurrency(data.corporate || 0, 0)}`}</p>
                  <p className="text-green-600">{`Imaging: ${formatAbbreviatedCurrency(data.imaging || 0, 0)}`}</p>
                  <p className="text-amber-600">{`Oncology: ${formatAbbreviatedCurrency(data.oncology || 0, 0)}`}</p>
                  <p className="text-red-600">{`Paediatrics: ${formatAbbreviatedCurrency(data.paediatrics || 0, 0)}`}</p>
                  <p className="text-pink-600">{`Women's Health: ${formatAbbreviatedCurrency(data.womensHealth || 0, 0)}`}</p>
                  <p className="text-gray-600">{`Others: ${formatAbbreviatedCurrency(data.others || 0, 0)}`}</p>
                </div>
              </>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Total: {totalCurrentPatients.toLocaleString()}
          </div>
          <div
            className={`${patientGrowthPercent >= 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"} rounded-full px-2.5 py-0.5 text-xs font-medium`}
          >
            Growth: {patientGrowthPercent >= 0 ? "+" : ""}
            {patientGrowthPercent}%
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={waterfallData}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />

          <Bar
            dataKey="base"
            stackId="stack"
            isAnimationActive={false}
            fill="transparent"
            legendType="none"
            tooltipType="none"
          />

          <Bar
            dataKey="aesthetics"
            name="Aesthetics"
            stackId="stack"
            fill={segmentColors.aesthetics}
          />

          <Bar
            dataKey="corporate"
            name="Corporate"
            stackId="stack"
            fill={segmentColors.corporate}
          />

          <Bar
            dataKey="imaging"
            name="Imaging"
            stackId="stack"
            fill={segmentColors.imaging}
          />

          <Bar
            dataKey="oncology"
            name="Oncology"
            stackId="stack"
            fill={segmentColors.oncology}
          />

          <Bar
            dataKey="paediatrics"
            name="Paediatrics"
            stackId="stack"
            fill={segmentColors.paediatrics}
          />

          <Bar
            dataKey="womensHealth"
            name="Women's Health"
            stackId="stack"
            fill={segmentColors.womensHealth}
          />

          <Bar
            dataKey="others"
            name="Others"
            stackId="stack"
            fill={segmentColors.others}
          >
            {/* Labels for total on top of each bar */}
            <LabelList
              dataKey="newTotal"
              position="top"
              formatter={(value: any) => {
                if (value === 0 || !value) return ""
                return formatAbbreviatedCurrency(value, 0)
              }}
              fill="#000000"
              fontSize={12}
            />
          </Bar>
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-2 gap-2">
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Top Segment</p>
          <p className="text-lg font-bold text-purple-600">
            {currentMonth.oncology > currentMonth.aesthetics &&
            currentMonth.oncology > currentMonth.corporate &&
            currentMonth.oncology > currentMonth.imaging &&
            currentMonth.oncology > currentMonth.paediatrics &&
            currentMonth.oncology > currentMonth.womensHealth &&
            currentMonth.oncology > currentMonth.others
              ? "Oncology"
              : currentMonth.imaging > currentMonth.aesthetics &&
                  currentMonth.imaging > currentMonth.corporate &&
                  currentMonth.imaging > currentMonth.paediatrics &&
                  currentMonth.imaging > currentMonth.womensHealth &&
                  currentMonth.imaging > currentMonth.others
                ? "Imaging"
                : "Other"}
          </p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Monthly Growth</p>
          <p className="text-lg font-bold text-green-600">
            {avgMonthlyGrowth}%
          </p>
        </div>
      </div>
    </div>
  )
}

export default PatientGrowthChart
