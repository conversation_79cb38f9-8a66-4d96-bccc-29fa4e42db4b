"use client"

import React, { useState } from "react"
import {
  Bar,
  CartesianGrid,
  Cell,
  ComposedChart,
  LabelList,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { CHART_HEIGHT } from "@/components/CardComponents"
import { BusinessUnitPMVData } from "@/components/chart-repository/data/priceMixVolumeData"
import {
  generateAllWaterfallData,
  WaterfallData,
} from "@/components/chart-repository/data/waterfallData"

interface WaterfallChartItem {
  name: string
  base: number // Starting point for each bar
  newBase: number // Ending point (cumulative total)
  deltaPositive: number // Positive delta (only for positive changes)
  deltaNegative: number // Negative delta (only for negative changes)
  actualValue: number // The actual delta value (for tooltips)
  fill: string // Color of the bar
  isTotal: boolean // Whether this is a total bar (first or last)
  percentage?: number // Percentage effect (for tooltips)
}

const PriceVolumeWaterfallChart = ({ data }: { data: BusinessUnitPMVData }) => {
  // Generate waterfall data for all business units
  const allWaterfallData = generateAllWaterfallData(
    data.businessUnits.map((unit) => ({
      name: unit.name,
      previousYearRevenue: unit.previousYearRevenue,
      currentYearRevenue: unit.currentYearRevenue,
      priceEffect: unit.priceEffect,
      mixEffect: unit.mixEffect,
      volumeEffect: unit.volumeEffect,
    }))
  )

  // State to track which business unit is selected
  const [selectedBusinessUnit, setSelectedBusinessUnit] =
    useState<string>("Total SMG")

  // Get the waterfall data for the selected business unit
  const selectedWaterfallData =
    allWaterfallData.find(
      (item) => item.businessUnit === selectedBusinessUnit
    ) || allWaterfallData[0]

  // Format the waterfall data for the chart
  const formatWaterfallData = (
    waterfallData: WaterfallData
  ): WaterfallChartItem[] => {
    if (
      !waterfallData ||
      !waterfallData.items ||
      waterfallData.items.length === 0
    ) {
      return []
    }

    const result: WaterfallChartItem[] = []
    let runningTotal = 0

    // First item (previous year revenue)
    result.push({
      name: `${data.year - 1} YTD`,
      base: 0, // Starts at 0
      newBase: waterfallData.previousYearRevenue, // Ends at previous year revenue
      deltaPositive: waterfallData.previousYearRevenue, // Full bar is positive
      deltaNegative: 0, // No negative component
      actualValue: waterfallData.previousYearRevenue,
      fill: "#90CAF9", // Light blue for previous year
      isTotal: true,
    })

    runningTotal = waterfallData.previousYearRevenue

    // Price effect
    const priceValue =
      (waterfallData.previousYearRevenue * waterfallData.priceEffect) / 100
    const newTotalAfterPrice = runningTotal + priceValue

    result.push({
      name: "Price",
      base: priceValue < 0 ? newTotalAfterPrice : runningTotal, // For negative values, base starts at the new lower total
      newBase: newTotalAfterPrice, // End at new total
      deltaPositive: priceValue > 0 ? priceValue : 0, // Only positive if value is positive
      deltaNegative: priceValue < 0 ? Math.abs(priceValue) : 0, // Only negative if value is negative
      actualValue: priceValue,
      fill: priceValue >= 0 ? "#4CAF50" : "#F44336", // Green for positive, red for negative
      percentage: waterfallData.priceEffect,
      isTotal: false,
    })

    runningTotal = newTotalAfterPrice

    // Mix effect
    const mixValue =
      (waterfallData.previousYearRevenue * waterfallData.mixEffect) / 100
    const newTotalAfterMix = runningTotal + mixValue

    result.push({
      name: "Mix",
      base: mixValue < 0 ? newTotalAfterMix : runningTotal, // For negative values, base starts at the new lower total
      newBase: newTotalAfterMix, // End at new total
      deltaPositive: mixValue > 0 ? mixValue : 0, // Only positive if value is positive
      deltaNegative: mixValue < 0 ? Math.abs(mixValue) : 0, // Only negative if value is negative
      actualValue: mixValue,
      fill: mixValue >= 0 ? "#4CAF50" : "#F44336", // Green for positive, red for negative
      percentage: waterfallData.mixEffect,
      isTotal: false,
    })

    runningTotal = newTotalAfterMix

    // Volume effect
    const volumeValue =
      (waterfallData.previousYearRevenue * waterfallData.volumeEffect) / 100
    const newTotalAfterVolume = runningTotal + volumeValue

    result.push({
      name: "Volume",
      base: volumeValue < 0 ? newTotalAfterVolume : runningTotal, // For negative values, base starts at the new lower total
      newBase: newTotalAfterVolume, // End at new total
      deltaPositive: volumeValue > 0 ? volumeValue : 0, // Only positive if value is positive
      deltaNegative: volumeValue < 0 ? Math.abs(volumeValue) : 0, // Only negative if value is negative
      actualValue: volumeValue,
      fill: volumeValue >= 0 ? "#4CAF50" : "#F44336", // Green for positive, red for negative
      percentage: waterfallData.volumeEffect,
      isTotal: false,
    })

    runningTotal = newTotalAfterVolume

    // Last item (current year revenue) - should match the calculated total
    result.push({
      name: `${data.year} YTD`,
      base: 0, // Starts at 0
      newBase: runningTotal, // Ends at final total
      deltaPositive: runningTotal, // Full bar is positive
      deltaNegative: 0, // No negative component
      actualValue: runningTotal,
      fill: "#0D47A1", // Dark blue for current year
      isTotal: true,
    })

    return result
  }

  const chartData = formatWaterfallData(selectedWaterfallData)

  // We don't need additional transformation - the formatWaterfallData function already prepared the data correctly
  const waterfallData: WaterfallChartItem[] = chartData

  const formatYAxis = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`
    } else {
      return `$${value}`
    }
  }

  // Format values for labels with +/- sign
  const formatLabelValue = (value: number) => {
    const sign = value > 0 ? "+" : ""
    if (Math.abs(value) >= 1000000) {
      return `${sign}$${(value / 1000000).toFixed(1)}M`
    } else if (Math.abs(value) >= 1000) {
      return `${sign}$${(value / 1000).toFixed(0)}K`
    } else {
      return `${sign}$${value}`
    }
  }

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const item = payload[0].payload
      const isEffect =
        label !== `${data.year - 1} YTD` && label !== `${data.year} YTD`
      const value = item.actualValue

      return (
        <div className="rounded border border-gray-300 bg-white p-3 shadow-lg">
          <p className="mb-2 text-lg font-bold text-gray-900">{label}</p>
          <div className="space-y-1">
            {isEffect ? (
              <>
                <p className="font-medium text-gray-800">
                  Value:{" "}
                  <span className="font-bold">
                    ${(value / 1000000).toFixed(2)}M
                  </span>
                </p>
                <p
                  className={`font-medium ${value >= 0 ? "text-green-600" : "text-red-600"}`}
                >
                  Effect:{" "}
                  <span className="font-bold">
                    {item.percentage?.toFixed(1)}%
                  </span>
                </p>
                <p className="text-sm text-gray-700">
                  Base:{" "}
                  <span className="font-semibold">
                    ${(item.base / 1000000).toFixed(2)}M
                  </span>
                </p>
                <p className="text-sm text-gray-700">
                  New Total:{" "}
                  <span className="font-semibold">
                    ${(item.newBase / 1000000).toFixed(2)}M
                  </span>
                </p>
              </>
            ) : (
              <p className="font-medium text-gray-800">
                Revenue:{" "}
                <span className="font-bold">
                  ${(value / 1000000).toFixed(2)}M
                </span>
              </p>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  return (
    <div className="flex flex-col gap-2">
      <Select
        value={selectedBusinessUnit}
        onValueChange={(value) => setSelectedBusinessUnit(value)}
      >
        <SelectTrigger size="sm">
          <SelectValue placeholder="Default" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="Total SMG">Total SMG</SelectItem>
          {allWaterfallData.map((item) => (
            <SelectItem key={item.businessUnit} value={item.businessUnit}>
              {item.businessUnit}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={waterfallData}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" tick={{ fontSize: 12, fill: "#000000" }} />
          <YAxis
            tickFormatter={formatYAxis}
            axisLine={false}
            tickLine={false}
            domain={[900000, "dataMax + 100000"]} // Set slightly below 1M to ensure visibility
            tick={{ fontSize: 12, fill: "#000000" }}
            width={60}
            scale="linear"
            allowDataOverflow={true}
          />

          {/* Add reference line at 1M to create visual base */}
          <ReferenceLine y={1000000} stroke="#ccc" strokeDasharray="3 3" />
          <Tooltip content={<CustomTooltip />} />

          {/* Base bars - white for middle items, transparent for totals */}
          <Bar dataKey="base" stackId="stack" isAnimationActive={false}>
            {waterfallData.map((entry, index) => {
              if (!entry) return null
              // White for Price, Mix, Volume; transparent for first and last bars
              return <Cell key={`cell-base-${index}`} fill="transparent" />
            })}
          </Bar>

          {/* Positive delta bars */}
          <Bar dataKey="deltaPositive" stackId="stack" name="Positive">
            {waterfallData.map((entry, index) => {
              if (!entry) return null
              return (
                <Cell
                  key={`cell-pos-${index}`}
                  fill={entry.deltaPositive > 0 ? entry.fill : "transparent"}
                />
              )
            })}
          </Bar>

          {/* Negative delta bars */}
          <Bar dataKey="deltaNegative" stackId="stack" name="Negative">
            {waterfallData.map((entry, index) => {
              if (!entry) return null
              return (
                <Cell
                  key={`cell-neg-${index}`}
                  fill={entry.deltaNegative > 0 ? "#F44336" : "transparent"}
                />
              )
            })}

            {/* Labels for all bars */}
            <LabelList
              dataKey="actualValue"
              position="top"
              formatter={(value: any) => {
                if (value === 0) return ""
                return formatLabelValue(value)
              }}
              fill="#000000"
              fontSize={12}
            />
          </Bar>
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  )
}

export default PriceVolumeWaterfallChart
